# CIFAR-10 Deep Neural Network Analysis Project

A comprehensive deep learning project that implements and compares multiple neural network architectures for CIFAR-10 image classification using TensorFlow/Keras.

## Project Overview

This project implements various deep learning architectures to classify images from the CIFAR-10 dataset, which contains 60,000 32x32 color images in 10 classes: airplane, automobile, bird, cat, deer, dog, frog, horse, ship, and truck.

## Features

### 🏗️ Multiple Architectures Implemented
- **Baseline CNN**: Simple convolutional neural network
- **Improved CNN**: Enhanced CNN with batch normalization and dropout
- **ResNet-20**: Residual network adapted for CIFAR-10
- **VGG-like**: VGG-inspired architecture with modern improvements
- **Attention CNN**: CNN with attention mechanism
- **Custom DenseNet**: DenseNet-inspired architecture

### 🚀 Performance Optimization Techniques
- **Data Augmentation**: Rotation, shifting, flipping, and zooming
- **Regularization**: Dropout, batch normalization, L2 regularization
- **Advanced Optimizers**: Adam with learning rate scheduling
- **Callbacks**: Early stopping, model checkpointing, learning rate reduction

### 📊 Comprehensive Analysis
- Training progress visualization
- Performance comparison across models
- Confusion matrices and classification reports
- Model architecture summaries
- Training time analysis

## Project Structure

```
├── cifar10_analysis.py      # Main data loading and basic models
├── advanced_models.py       # Advanced architectures (ResNet, VGG, etc.)
├── training_pipeline.py     # Training and evaluation pipeline
├── main_experiment.py       # Main execution script
├── requirements.txt         # Python dependencies
├── README.md               # This file
├── models/                 # Saved models directory
├── plots/                  # Generated plots directory
└── results/                # Training results directory
```

## Installation

1. Clone or download this project
2. Install required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Quick Start
Run the main experiment script:
```bash
python main_experiment.py
```

Choose from three options:
1. **Complete Experiment**: Full training with all models (~2-3 hours)
2. **Quick Experiment**: Reduced epochs for testing (~30 minutes)
3. **Data Exploration**: Just explore the dataset and model architectures

### Individual Components

#### Data Loading and Preprocessing
```python
from cifar10_analysis import CIFAR10DataLoader

data_loader = CIFAR10DataLoader()
(x_train, y_train), (x_test, y_test) = data_loader.load_data()
x_train, y_train, x_test, y_test = data_loader.preprocess_data(
    x_train, y_train, x_test, y_test
)
```

#### Training a Single Model
```python
from cifar10_analysis import ModelArchitectures
from training_pipeline import ModelTrainer

# Create model
models = ModelArchitectures()
model = models.improved_cnn()

# Train model
trainer = ModelTrainer(x_train, y_train, x_test, y_test, class_names)
trained_model, history = trainer.train_model(
    model, "Improved CNN", epochs=50, use_augmentation=True
)
```

## Model Architectures

### 1. Baseline CNN
- Simple 3-layer CNN
- Basic architecture for comparison
- ~100K parameters

### 2. Improved CNN
- Batch normalization and dropout
- Multiple convolutional blocks
- ~500K parameters

### 3. ResNet-20
- Residual connections
- Skip connections for better gradient flow
- ~270K parameters

### 4. VGG-like Model
- Deep architecture with small filters
- Multiple convolutional blocks
- ~2M parameters

### 5. Attention CNN
- Attention mechanism for feature focusing
- Improved feature selection
- ~800K parameters

## Performance Optimization Techniques

### Data Augmentation
- Rotation: ±15 degrees
- Width/Height shift: 10%
- Horizontal flip
- Zoom: 10%

### Regularization
- Dropout: 0.25-0.5
- Batch normalization after conv layers
- L2 regularization: 0.0001

### Training Strategies
- Learning rate scheduling
- Early stopping with patience
- Model checkpointing
- Reduce LR on plateau

## Expected Results

Based on typical performance on CIFAR-10:

| Model | Expected Accuracy | Training Time |
|-------|------------------|---------------|
| Baseline CNN | ~70% | 10 minutes |
| Improved CNN | ~80% | 20 minutes |
| ResNet-20 | ~85% | 30 minutes |
| VGG-like | ~82% | 25 minutes |
| Attention CNN | ~83% | 25 minutes |

## Output Files

The project generates several output files:

### Models
- `models/best_[model_name].h5`: Best model checkpoints
- `models/best_overall_model.h5`: Best performing model

### Visualizations
- `cifar10_samples.png`: Sample images from dataset
- `training_history_[model].png`: Training curves
- `confusion_matrix_[model].png`: Confusion matrices
- `model_comparison.png`: Performance comparison

### Results
- `results/complete_experiment_results.json`: Detailed results
- Training logs and metrics

## Key Features

### 🔧 Modular Design
- Separate modules for different functionalities
- Easy to extend with new architectures
- Clean separation of concerns

### 📈 Comprehensive Evaluation
- Multiple metrics (accuracy, loss, training time)
- Visual comparisons
- Detailed classification reports

### 🎯 Performance Focused
- Multiple optimization techniques
- Hyperparameter tuning
- Best practices implementation

### 📊 Rich Visualizations
- Training progress plots
- Confusion matrices
- Performance comparisons
- Sample data visualization

## Customization

### Adding New Models
1. Add your model function to `advanced_models.py`
2. Update the `models_to_train` list in `main_experiment.py`
3. Run the experiment

### Modifying Hyperparameters
- Edit the model configurations in `main_experiment.py`
- Adjust training parameters in `training_pipeline.py`
- Modify data augmentation in `cifar10_analysis.py`

## Requirements

- Python 3.7+
- TensorFlow 2.10+
- NumPy, Matplotlib, Seaborn
- Scikit-learn for metrics
- 4GB+ RAM recommended
- GPU recommended for faster training

## Tips for Best Results

1. **Use GPU**: Training will be much faster with CUDA-enabled GPU
2. **Monitor Training**: Watch for overfitting and adjust regularization
3. **Experiment**: Try different hyperparameters and architectures
4. **Data Quality**: Ensure proper preprocessing and augmentation
5. **Patience**: Some models may need more epochs to converge

## Troubleshooting

### Common Issues
- **Out of Memory**: Reduce batch size or model complexity
- **Slow Training**: Use GPU or reduce model size
- **Poor Performance**: Check data preprocessing and model architecture
- **Overfitting**: Increase regularization or reduce model complexity

### Performance Tips
- Use mixed precision training for faster computation
- Implement gradient clipping for stable training
- Use learning rate warmup for better convergence
- Monitor validation metrics to prevent overfitting

## License

This project is for educational purposes. Feel free to use and modify for learning and research.

## Contributing

Contributions are welcome! Please feel free to submit improvements, bug fixes, or new model architectures.
