"""
Test Script for CIFAR-10 Deep Learning Project Setup
===================================================

This script tests the basic functionality of all modules to ensure
everything is working correctly before running the full experiment.
"""

import sys
import numpy as np
import tensorflow as tf

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        from cifar10_analysis import CIFAR10DataLoader, ModelArchitectures
        print("✓ cifar10_analysis imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import cifar10_analysis: {e}")
        return False
    
    try:
        from advanced_models import AdvancedArchitectures
        print("✓ advanced_models imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import advanced_models: {e}")
        return False
    
    try:
        from training_pipeline import ModelTrainer, create_directories
        print("✓ training_pipeline imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import training_pipeline: {e}")
        return False
    
    return True

def test_data_loading():
    """Test data loading functionality"""
    print("\nTesting data loading...")
    
    try:
        from cifar10_analysis import CIFAR10DataLoader
        
        data_loader = CIFAR10DataLoader()
        print("✓ DataLoader initialized")
        
        # Load a small subset for testing
        (x_train, y_train), (x_test, y_test) = data_loader.load_data()
        print(f"✓ Data loaded - Train: {x_train.shape}, Test: {x_test.shape}")
        
        # Test preprocessing
        x_train_small = x_train[:100]  # Use small subset
        y_train_small = y_train[:100]
        x_test_small = x_test[:100]
        y_test_small = y_test[:100]
        
        x_train_prep, y_train_prep, x_test_prep, y_test_prep = data_loader.preprocess_data(
            x_train_small, y_train_small, x_test_small, y_test_small
        )
        print("✓ Data preprocessing successful")
        
        return True, (x_train_prep, y_train_prep, x_test_prep, y_test_prep)
        
    except Exception as e:
        print(f"✗ Data loading failed: {e}")
        return False, None

def test_model_creation():
    """Test model creation"""
    print("\nTesting model creation...")
    
    try:
        from cifar10_analysis import ModelArchitectures
        from advanced_models import AdvancedArchitectures
        
        # Test basic models
        basic_models = ModelArchitectures()
        
        baseline = basic_models.baseline_cnn()
        print(f"✓ Baseline CNN created - Parameters: {baseline.count_params():,}")
        
        improved = basic_models.improved_cnn()
        print(f"✓ Improved CNN created - Parameters: {improved.count_params():,}")
        
        # Test advanced models
        advanced_models = AdvancedArchitectures()
        
        resnet = advanced_models.resnet_cifar10(depth=20)
        print(f"✓ ResNet-20 created - Parameters: {resnet.count_params():,}")
        
        vgg = advanced_models.vgg_like_model()
        print(f"✓ VGG-like model created - Parameters: {vgg.count_params():,}")
        
        attention = advanced_models.attention_cnn()
        print(f"✓ Attention CNN created - Parameters: {attention.count_params():,}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return False

def test_training_pipeline():
    """Test training pipeline with minimal training"""
    print("\nTesting training pipeline...")
    
    try:
        from cifar10_analysis import CIFAR10DataLoader, ModelArchitectures
        from training_pipeline import ModelTrainer, create_directories
        
        # Create directories
        create_directories()
        print("✓ Directories created")
        
        # Load minimal data
        data_loader = CIFAR10DataLoader()
        (x_train, y_train), (x_test, y_test) = data_loader.load_data()
        
        # Use very small subset for quick test
        x_train_tiny = x_train[:200]
        y_train_tiny = y_train[:200]
        x_test_tiny = x_test[:100]
        y_test_tiny = y_test[:100]
        
        x_train_prep, y_train_prep, x_test_prep, y_test_prep = data_loader.preprocess_data(
            x_train_tiny, y_train_tiny, x_test_tiny, y_test_tiny
        )
        
        # Initialize trainer
        trainer = ModelTrainer(x_train_prep, y_train_prep, x_test_prep, y_test_prep, 
                              data_loader.class_names)
        print("✓ Trainer initialized")
        
        # Create and train a simple model for 1 epoch
        models = ModelArchitectures()
        test_model = models.baseline_cnn()
        
        print("✓ Starting minimal training test (1 epoch)...")
        trained_model, history = trainer.train_model(
            model=test_model,
            model_name="Test Model",
            epochs=1,
            batch_size=32,
            use_augmentation=False
        )
        
        print("✓ Training pipeline test successful")
        return True
        
    except Exception as e:
        print(f"✗ Training pipeline test failed: {e}")
        return False

def test_tensorflow_gpu():
    """Test TensorFlow GPU availability"""
    print("\nTesting TensorFlow setup...")
    
    print(f"TensorFlow version: {tf.__version__}")
    
    # Check GPU availability
    gpus = tf.config.list_physical_devices('GPU')
    if gpus:
        print(f"✓ Found {len(gpus)} GPU(s):")
        for i, gpu in enumerate(gpus):
            print(f"  GPU {i}: {gpu}")
    else:
        print("⚠ No GPU found - training will use CPU (slower)")
    
    # Test basic TensorFlow operations
    try:
        with tf.device('/CPU:0'):
            a = tf.constant([1, 2, 3])
            b = tf.constant([4, 5, 6])
            c = tf.add(a, b)
        print("✓ TensorFlow CPU operations working")
        
        if gpus:
            with tf.device('/GPU:0'):
                a = tf.constant([1, 2, 3])
                b = tf.constant([4, 5, 6])
                c = tf.add(a, b)
            print("✓ TensorFlow GPU operations working")
            
    except Exception as e:
        print(f"✗ TensorFlow operations failed: {e}")
        return False
    
    return True

def main():
    """Run all tests"""
    print("CIFAR-10 Deep Learning Project - Setup Test")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Imports
    if test_imports():
        tests_passed += 1
    
    # Test 2: TensorFlow setup
    if test_tensorflow_gpu():
        tests_passed += 1
    
    # Test 3: Data loading
    data_success, data = test_data_loading()
    if data_success:
        tests_passed += 1
    
    # Test 4: Model creation
    if test_model_creation():
        tests_passed += 1
    
    # Test 5: Training pipeline (only if data loading worked)
    if data_success:
        if test_training_pipeline():
            tests_passed += 1
    else:
        print("\nSkipping training pipeline test due to data loading failure")
    
    # Summary
    print(f"\n{'='*50}")
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Your setup is ready.")
        print("\nYou can now run the main experiment:")
        print("python main_experiment.py")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
        print("\nCommon solutions:")
        print("- Install missing packages: pip install -r requirements.txt")
        print("- Check TensorFlow installation: pip install tensorflow")
        print("- Ensure sufficient memory for data loading")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
