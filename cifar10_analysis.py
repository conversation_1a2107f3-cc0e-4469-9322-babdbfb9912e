"""
CIFAR-10 Deep Neural Network Analysis Project
============================================

This project implements and compares different deep learning architectures
for CIFAR-10 image classification using TensorFlow/Keras.

Author: DL Project
Date: 2025-08-08
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, models, optimizers, callbacks
from tensorflow.keras.datasets import cifar10
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import os
import json
from datetime import datetime

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

class CIFAR10DataLoader:
    """
    Handles CIFAR-10 data loading and preprocessing
    """
    
    def __init__(self):
        self.class_names = ['airplane', 'automobile', 'bird', 'cat', 'deer',
                           'dog', 'frog', 'horse', 'ship', 'truck']
        self.num_classes = 10
        self.input_shape = (32, 32, 3)
        
    def load_data(self):
        """Load CIFAR-10 dataset"""
        print("Loading CIFAR-10 dataset...")
        (x_train, y_train), (x_test, y_test) = cifar10.load_data()
        
        print(f"Training data shape: {x_train.shape}")
        print(f"Training labels shape: {y_train.shape}")
        print(f"Test data shape: {x_test.shape}")
        print(f"Test labels shape: {y_test.shape}")
        
        return (x_train, y_train), (x_test, y_test)
    
    def preprocess_data(self, x_train, y_train, x_test, y_test, normalize=True):
        """
        Preprocess the data with normalization and label encoding
        """
        print("Preprocessing data...")
        
        # Convert to float32 for better performance
        x_train = x_train.astype('float32')
        x_test = x_test.astype('float32')
        
        if normalize:
            # Normalize pixel values to [0, 1]
            x_train = x_train / 255.0
            x_test = x_test / 255.0
            print("Data normalized to [0, 1] range")
        
        # Convert labels to categorical one-hot encoding
        y_train_cat = to_categorical(y_train, self.num_classes)
        y_test_cat = to_categorical(y_test, self.num_classes)
        
        print(f"Preprocessed training data shape: {x_train.shape}")
        print(f"Preprocessed training labels shape: {y_train_cat.shape}")
        
        return x_train, y_train_cat, x_test, y_test_cat
    
    def create_data_augmentation(self):
        """
        Create data augmentation generator for training
        """
        datagen = ImageDataGenerator(
            rotation_range=15,
            width_shift_range=0.1,
            height_shift_range=0.1,
            horizontal_flip=True,
            zoom_range=0.1,
            fill_mode='nearest'
        )
        return datagen
    
    def visualize_samples(self, x_data, y_data, num_samples=10):
        """
        Visualize sample images from the dataset
        """
        plt.figure(figsize=(15, 6))
        for i in range(num_samples):
            plt.subplot(2, 5, i + 1)
            plt.imshow(x_data[i])
            plt.title(f'{self.class_names[np.argmax(y_data[i])]}')
            plt.axis('off')
        plt.suptitle('CIFAR-10 Sample Images')
        plt.tight_layout()
        plt.savefig('cifar10_samples.png', dpi=300, bbox_inches='tight')
        plt.show()

class ModelArchitectures:
    """
    Contains different neural network architectures for CIFAR-10
    """
    
    def __init__(self, input_shape=(32, 32, 3), num_classes=10):
        self.input_shape = input_shape
        self.num_classes = num_classes
    
    def baseline_cnn(self):
        """
        Baseline CNN architecture
        """
        model = models.Sequential([
            layers.Conv2D(32, (3, 3), activation='relu', input_shape=self.input_shape),
            layers.MaxPooling2D((2, 2)),
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.MaxPooling2D((2, 2)),
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.Flatten(),
            layers.Dense(64, activation='relu'),
            layers.Dense(self.num_classes, activation='softmax')
        ])
        
        model.compile(
            optimizer='adam',
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def improved_cnn(self):
        """
        Improved CNN with batch normalization and dropout
        """
        model = models.Sequential([
            layers.Conv2D(32, (3, 3), activation='relu', input_shape=self.input_shape),
            layers.BatchNormalization(),
            layers.Conv2D(32, (3, 3), activation='relu'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            layers.Conv2D(128, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.25),
            
            layers.Flatten(),
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(self.num_classes, activation='softmax')
        ])
        
        model.compile(
            optimizer=optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model

def main():
    """
    Main function to demonstrate data loading and preprocessing
    """
    print("CIFAR-10 Deep Neural Network Analysis")
    print("=" * 40)
    
    # Initialize data loader
    data_loader = CIFAR10DataLoader()
    
    # Load data
    (x_train, y_train), (x_test, y_test) = data_loader.load_data()
    
    # Preprocess data
    x_train_prep, y_train_prep, x_test_prep, y_test_prep = data_loader.preprocess_data(
        x_train, y_train, x_test, y_test
    )
    
    # Visualize samples
    data_loader.visualize_samples(x_train_prep, y_train_prep)
    
    # Create baseline model
    print("\nCreating baseline CNN model...")
    model_arch = ModelArchitectures()
    baseline_model = model_arch.baseline_cnn()
    baseline_model.summary()
    
    print("\nProject setup completed successfully!")
    print("Next steps: Implement training and evaluation pipeline")

if __name__ == "__main__":
    main()
