"""
Main Experiment Script for CIFAR-10 Deep Learning Analysis
=========================================================

This script runs the complete experiment comparing different neural network
architectures on the CIFAR-10 dataset.
"""

import os
import sys
import numpy as np
import tensorflow as tf
from tensorflow import keras

# Import our custom modules
from cifar10_analysis import CIFAR10DataLoader, ModelArchitectures
from advanced_models import AdvancedArchitectures, create_callbacks
from training_pipeline import ModelTrainer, create_directories

# Set memory growth for GPU (if available)
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"Found {len(gpus)} GPU(s)")
    except RuntimeError as e:
        print(e)

def run_complete_experiment():
    """
    Run the complete CIFAR-10 experiment with multiple architectures
    """
    print("CIFAR-10 Deep Neural Network Analysis - Complete Experiment")
    print("=" * 60)
    
    # Create necessary directories
    create_directories()
    
    # Initialize data loader
    print("\n1. Loading and preprocessing data...")
    data_loader = CIFAR10DataLoader()
    (x_train, y_train), (x_test, y_test) = data_loader.load_data()
    x_train, y_train, x_test, y_test = data_loader.preprocess_data(
        x_train, y_train, x_test, y_test
    )
    
    # Create data augmentation generator
    datagen = data_loader.create_data_augmentation()
    
    # Visualize sample data
    data_loader.visualize_samples(x_train, y_train)
    
    # Initialize model architectures
    basic_models = ModelArchitectures()
    advanced_models = AdvancedArchitectures()
    
    # Initialize trainer
    trainer = ModelTrainer(x_train, y_train, x_test, y_test, data_loader.class_names)
    
    # Define models to train
    models_to_train = [
        {
            'name': 'Baseline CNN',
            'model_func': basic_models.baseline_cnn,
            'epochs': 30,
            'use_augmentation': False
        },
        {
            'name': 'Improved CNN',
            'model_func': basic_models.improved_cnn,
            'epochs': 50,
            'use_augmentation': True
        },
        {
            'name': 'ResNet-20',
            'model_func': lambda: advanced_models.resnet_cifar10(depth=20),
            'epochs': 60,
            'use_augmentation': True
        },
        {
            'name': 'VGG-like',
            'model_func': advanced_models.vgg_like_model,
            'epochs': 50,
            'use_augmentation': True
        },
        {
            'name': 'Attention CNN',
            'model_func': advanced_models.attention_cnn,
            'epochs': 45,
            'use_augmentation': True
        }
    ]
    
    print(f"\n2. Training {len(models_to_train)} different architectures...")
    
    # Train each model
    trained_models = {}
    for i, model_config in enumerate(models_to_train, 1):
        print(f"\n--- Training Model {i}/{len(models_to_train)}: {model_config['name']} ---")
        
        # Create model
        model = model_config['model_func']()
        print(f"Model parameters: {model.count_params():,}")
        
        # Train model
        trained_model, history = trainer.train_model(
            model=model,
            model_name=model_config['name'],
            epochs=model_config['epochs'],
            batch_size=32,
            use_augmentation=model_config['use_augmentation'],
            datagen=datagen if model_config['use_augmentation'] else None
        )
        
        trained_models[model_config['name']] = trained_model
        
        # Plot training history
        trainer.plot_training_history(model_config['name'])
        
        # Evaluate model
        trainer.evaluate_model(model_config['name'])
    
    print("\n3. Comparing all models...")
    comparison_results = trainer.compare_models()
    
    # Save results
    trainer.save_results('results/complete_experiment_results.json')
    
    print("\n4. Experiment Summary:")
    print("=" * 50)
    print(f"Total models trained: {len(trained_models)}")
    print(f"Best performing model: {comparison_results[0]['Model']}")
    print(f"Best accuracy: {comparison_results[0]['Test Accuracy']:.4f}")
    
    # Save the best model
    best_model_name = comparison_results[0]['Model']
    best_model = trained_models[best_model_name]
    best_model.save(f'models/best_overall_model.h5')
    print(f"Best model saved as: models/best_overall_model.h5")
    
    return trainer, trained_models, comparison_results

def run_quick_experiment():
    """
    Run a quick experiment with fewer epochs for testing
    """
    print("CIFAR-10 Quick Experiment (Reduced Epochs)")
    print("=" * 45)
    
    create_directories()
    
    # Load data
    data_loader = CIFAR10DataLoader()
    (x_train, y_train), (x_test, y_test) = data_loader.load_data()
    x_train, y_train, x_test, y_test = data_loader.preprocess_data(
        x_train, y_train, x_test, y_test
    )
    
    datagen = data_loader.create_data_augmentation()
    
    # Initialize models and trainer
    basic_models = ModelArchitectures()
    trainer = ModelTrainer(x_train, y_train, x_test, y_test, data_loader.class_names)
    
    # Quick models to test
    quick_models = [
        {
            'name': 'Baseline CNN (Quick)',
            'model_func': basic_models.baseline_cnn,
            'epochs': 10,
            'use_augmentation': False
        },
        {
            'name': 'Improved CNN (Quick)',
            'model_func': basic_models.improved_cnn,
            'epochs': 15,
            'use_augmentation': True
        }
    ]
    
    trained_models = {}
    for model_config in quick_models:
        print(f"\nTraining: {model_config['name']}")
        
        model = model_config['model_func']()
        trained_model, history = trainer.train_model(
            model=model,
            model_name=model_config['name'],
            epochs=model_config['epochs'],
            batch_size=32,
            use_augmentation=model_config['use_augmentation'],
            datagen=datagen if model_config['use_augmentation'] else None
        )
        
        trained_models[model_config['name']] = trained_model
        trainer.plot_training_history(model_config['name'])
    
    # Compare results
    comparison_results = trainer.compare_models()
    trainer.save_results('results/quick_experiment_results.json')
    
    return trainer, trained_models, comparison_results

def main():
    """
    Main function with experiment options
    """
    print("CIFAR-10 Deep Learning Analysis")
    print("Choose experiment type:")
    print("1. Complete Experiment (Full training, ~2-3 hours)")
    print("2. Quick Experiment (Reduced epochs, ~30 minutes)")
    print("3. Data Exploration Only")
    
    choice = input("Enter your choice (1/2/3): ").strip()
    
    if choice == '1':
        return run_complete_experiment()
    elif choice == '2':
        return run_quick_experiment()
    elif choice == '3':
        # Just explore the data
        data_loader = CIFAR10DataLoader()
        (x_train, y_train), (x_test, y_test) = data_loader.load_data()
        x_train, y_train, x_test, y_test = data_loader.preprocess_data(
            x_train, y_train, x_test, y_test
        )
        data_loader.visualize_samples(x_train, y_train)
        
        # Show model architectures
        basic_models = ModelArchitectures()
        advanced_models = AdvancedArchitectures()
        
        print("\nBaseline CNN Architecture:")
        baseline = basic_models.baseline_cnn()
        baseline.summary()
        
        print("\nImproved CNN Architecture:")
        improved = basic_models.improved_cnn()
        improved.summary()
        
        return None, None, None
    else:
        print("Invalid choice. Exiting.")
        return None, None, None

if __name__ == "__main__":
    trainer, models, results = main()
