"""
Training and Evaluation Pipeline for CIFAR-10 Models
===================================================

This module handles model training, evaluation, and performance comparison.
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix
import tensorflow as tf
from tensorflow import keras
import json
import time
from datetime import datetime
import os

class ModelTrainer:
    """
    Handles training and evaluation of different models
    """
    
    def __init__(self, x_train, y_train, x_test, y_test, class_names):
        self.x_train = x_train
        self.y_train = y_train
        self.x_test = x_test
        self.y_test = y_test
        self.class_names = class_names
        self.results = {}
        
        # Create validation split
        val_split = 0.2
        val_size = int(len(x_train) * val_split)
        
        self.x_val = x_train[-val_size:]
        self.y_val = y_train[-val_size:]
        self.x_train_split = x_train[:-val_size]
        self.y_train_split = y_train[:-val_size]
        
        print(f"Training set size: {len(self.x_train_split)}")
        print(f"Validation set size: {len(self.x_val)}")
        print(f"Test set size: {len(self.x_test)}")
    
    def train_model(self, model, model_name, epochs=50, batch_size=32, 
                   use_augmentation=False, datagen=None):
        """
        Train a model and record its performance
        """
        print(f"\nTraining {model_name}...")
        print("=" * 50)
        
        # Create callbacks
        callbacks = [
            keras.callbacks.ModelCheckpoint(
                f'models/best_{model_name.lower().replace(" ", "_")}.h5',
                monitor='val_accuracy',
                save_best_only=True,
                mode='max',
                verbose=1
            ),
            keras.callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=7,
                min_lr=1e-7,
                verbose=1
            )
        ]
        
        # Record training start time
        start_time = time.time()
        
        # Train with or without data augmentation
        if use_augmentation and datagen is not None:
            print("Training with data augmentation...")
            datagen.fit(self.x_train_split)
            history = model.fit(
                datagen.flow(self.x_train_split, self.y_train_split, batch_size=batch_size),
                steps_per_epoch=len(self.x_train_split) // batch_size,
                epochs=epochs,
                validation_data=(self.x_val, self.y_val),
                callbacks=callbacks,
                verbose=1
            )
        else:
            print("Training without data augmentation...")
            history = model.fit(
                self.x_train_split, self.y_train_split,
                batch_size=batch_size,
                epochs=epochs,
                validation_data=(self.x_val, self.y_val),
                callbacks=callbacks,
                verbose=1
            )
        
        # Record training time
        training_time = time.time() - start_time
        
        # Evaluate on test set
        test_loss, test_accuracy = model.evaluate(self.x_test, self.y_test, verbose=0)
        
        # Get predictions for detailed analysis
        y_pred = model.predict(self.x_test, verbose=0)
        y_pred_classes = np.argmax(y_pred, axis=1)
        y_true_classes = np.argmax(self.y_test, axis=1)
        
        # Store results
        self.results[model_name] = {
            'history': history.history,
            'test_accuracy': test_accuracy,
            'test_loss': test_loss,
            'training_time': training_time,
            'y_pred': y_pred_classes,
            'y_true': y_true_classes,
            'epochs_trained': len(history.history['loss'])
        }
        
        print(f"\n{model_name} Results:")
        print(f"Test Accuracy: {test_accuracy:.4f}")
        print(f"Test Loss: {test_loss:.4f}")
        print(f"Training Time: {training_time:.2f} seconds")
        print(f"Epochs Trained: {len(history.history['loss'])}")
        
        return model, history
    
    def evaluate_model(self, model_name):
        """
        Detailed evaluation of a trained model
        """
        if model_name not in self.results:
            print(f"No results found for {model_name}")
            return
        
        result = self.results[model_name]
        y_pred = result['y_pred']
        y_true = result['y_true']
        
        # Classification report
        print(f"\nClassification Report for {model_name}:")
        print("=" * 50)
        print(classification_report(y_true, y_pred, target_names=self.class_names))
        
        # Confusion matrix
        cm = confusion_matrix(y_true, y_pred)
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.class_names,
                   yticklabels=self.class_names)
        plt.title(f'Confusion Matrix - {model_name}')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.tight_layout()
        plt.savefig(f'confusion_matrix_{model_name.lower().replace(" ", "_")}.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_training_history(self, model_name):
        """
        Plot training history for a model
        """
        if model_name not in self.results:
            print(f"No results found for {model_name}")
            return
        
        history = self.results[model_name]['history']
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # Plot accuracy
        ax1.plot(history['accuracy'], label='Training Accuracy')
        ax1.plot(history['val_accuracy'], label='Validation Accuracy')
        ax1.set_title(f'{model_name} - Model Accuracy')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        ax1.grid(True)
        
        # Plot loss
        ax2.plot(history['loss'], label='Training Loss')
        ax2.plot(history['val_loss'], label='Validation Loss')
        ax2.set_title(f'{model_name} - Model Loss')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(f'training_history_{model_name.lower().replace(" ", "_")}.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def compare_models(self):
        """
        Compare performance of all trained models
        """
        if not self.results:
            print("No models have been trained yet.")
            return
        
        # Create comparison dataframe
        comparison_data = []
        for model_name, result in self.results.items():
            comparison_data.append({
                'Model': model_name,
                'Test Accuracy': result['test_accuracy'],
                'Test Loss': result['test_loss'],
                'Training Time (s)': result['training_time'],
                'Epochs': result['epochs_trained']
            })
        
        # Sort by test accuracy
        comparison_data.sort(key=lambda x: x['Test Accuracy'], reverse=True)
        
        print("\nModel Performance Comparison:")
        print("=" * 80)
        print(f"{'Model':<20} {'Accuracy':<10} {'Loss':<10} {'Time (s)':<12} {'Epochs':<8}")
        print("-" * 80)
        
        for data in comparison_data:
            print(f"{data['Model']:<20} {data['Test Accuracy']:<10.4f} "
                  f"{data['Test Loss']:<10.4f} {data['Training Time (s)']:<12.2f} "
                  f"{data['Epochs']:<8}")
        
        # Plot comparison
        models = [data['Model'] for data in comparison_data]
        accuracies = [data['Test Accuracy'] for data in comparison_data]
        
        plt.figure(figsize=(12, 6))
        bars = plt.bar(models, accuracies, color='skyblue', edgecolor='navy', alpha=0.7)
        plt.title('Model Performance Comparison - Test Accuracy')
        plt.xlabel('Model')
        plt.ylabel('Test Accuracy')
        plt.xticks(rotation=45, ha='right')
        plt.grid(axis='y', alpha=0.3)
        
        # Add value labels on bars
        for bar, acc in zip(bars, accuracies):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                    f'{acc:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return comparison_data
    
    def save_results(self, filename='training_results.json'):
        """
        Save training results to JSON file
        """
        # Prepare results for JSON serialization
        json_results = {}
        for model_name, result in self.results.items():
            json_results[model_name] = {
                'test_accuracy': float(result['test_accuracy']),
                'test_loss': float(result['test_loss']),
                'training_time': float(result['training_time']),
                'epochs_trained': int(result['epochs_trained']),
                'final_train_accuracy': float(result['history']['accuracy'][-1]),
                'final_val_accuracy': float(result['history']['val_accuracy'][-1]),
                'final_train_loss': float(result['history']['loss'][-1]),
                'final_val_loss': float(result['history']['val_loss'][-1])
            }
        
        # Add metadata
        json_results['metadata'] = {
            'timestamp': datetime.now().isoformat(),
            'total_models': len(self.results),
            'dataset': 'CIFAR-10'
        }
        
        with open(filename, 'w') as f:
            json.dump(json_results, f, indent=2)
        
        print(f"Results saved to {filename}")

def create_directories():
    """
    Create necessary directories for saving models and results
    """
    directories = ['models', 'plots', 'results']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")
