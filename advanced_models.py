"""
Advanced Neural Network Architectures for CIFAR-10
=================================================

This module contains advanced architectures including ResNet and custom designs
for improved CIFAR-10 classification performance.
"""

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, models, optimizers
from tensorflow.keras.regularizers import l2

class AdvancedArchitectures:
    """
    Advanced neural network architectures for CIFAR-10
    """
    
    def __init__(self, input_shape=(32, 32, 3), num_classes=10):
        self.input_shape = input_shape
        self.num_classes = num_classes
    
    def residual_block(self, x, filters, kernel_size=3, stride=1, conv_shortcut=False):
        """
        Residual block for ResNet architecture
        """
        if conv_shortcut:
            shortcut = layers.Conv2D(filters, 1, strides=stride, 
                                   kernel_regularizer=l2(0.0001))(x)
            shortcut = layers.BatchNormalization()(shortcut)
        else:
            shortcut = x
        
        x = layers.Conv2D(filters, kernel_size, strides=stride, padding='same',
                         kernel_regularizer=l2(0.0001))(x)
        x = layers.BatchNormalization()(x)
        x = layers.Activation('relu')(x)
        
        x = layers.Conv2D(filters, kernel_size, padding='same',
                         kernel_regularizer=l2(0.0001))(x)
        x = layers.BatchNormalization()(x)
        
        x = layers.Add()([shortcut, x])
        x = layers.Activation('relu')(x)
        return x
    
    def resnet_cifar10(self, depth=20):
        """
        ResNet architecture adapted for CIFAR-10
        """
        # Calculate number of residual blocks
        n = (depth - 2) // 6
        
        inputs = layers.Input(shape=self.input_shape)
        
        # Initial convolution
        x = layers.Conv2D(16, 3, padding='same', kernel_regularizer=l2(0.0001))(inputs)
        x = layers.BatchNormalization()(x)
        x = layers.Activation('relu')(x)
        
        # First stack of residual blocks
        for i in range(n):
            x = self.residual_block(x, 16)
        
        # Second stack of residual blocks
        x = self.residual_block(x, 32, stride=2, conv_shortcut=True)
        for i in range(n - 1):
            x = self.residual_block(x, 32)
        
        # Third stack of residual blocks
        x = self.residual_block(x, 64, stride=2, conv_shortcut=True)
        for i in range(n - 1):
            x = self.residual_block(x, 64)
        
        # Final layers
        x = layers.GlobalAveragePooling2D()(x)
        x = layers.Dense(self.num_classes, activation='softmax',
                        kernel_regularizer=l2(0.0001))(x)
        
        model = models.Model(inputs, x)
        
        model.compile(
            optimizer=optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def vgg_like_model(self):
        """
        VGG-like architecture adapted for CIFAR-10
        """
        model = models.Sequential([
            # Block 1
            layers.Conv2D(64, (3, 3), activation='relu', padding='same', 
                         input_shape=self.input_shape),
            layers.BatchNormalization(),
            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Block 2
            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Block 3
            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Classifier
            layers.Flatten(),
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(self.num_classes, activation='softmax')
        ])
        
        model.compile(
            optimizer=optimizers.Adam(learning_rate=0.0001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def densenet_block(self, x, growth_rate, name):
        """
        Dense block for DenseNet-like architecture
        """
        x1 = layers.BatchNormalization(name=name + '_0_bn')(x)
        x1 = layers.Activation('relu', name=name + '_0_relu')(x1)
        x1 = layers.Conv2D(4 * growth_rate, 1, use_bias=False,
                          name=name + '_1_conv')(x1)
        x1 = layers.BatchNormalization(name=name + '_1_bn')(x1)
        x1 = layers.Activation('relu', name=name + '_1_relu')(x1)
        x1 = layers.Conv2D(growth_rate, 3, padding='same', use_bias=False,
                          name=name + '_2_conv')(x1)
        x = layers.Concatenate(name=name + '_concat')([x, x1])
        return x
    
    def custom_densenet(self):
        """
        Custom DenseNet-like architecture for CIFAR-10
        """
        inputs = layers.Input(shape=self.input_shape)
        
        # Initial convolution
        x = layers.Conv2D(64, 7, strides=2, padding='same', use_bias=False)(inputs)
        x = layers.BatchNormalization()(x)
        x = layers.Activation('relu')(x)
        x = layers.MaxPooling2D(3, strides=2, padding='same')(x)
        
        # Dense blocks
        growth_rate = 32
        for i in range(4):
            x = self.densenet_block(x, growth_rate, f'conv{i+2}_block1')
        
        # Transition layer
        x = layers.BatchNormalization()(x)
        x = layers.Activation('relu')(x)
        x = layers.Conv2D(x.shape[-1] // 2, 1, use_bias=False)(x)
        x = layers.AveragePooling2D(2, strides=2)(x)
        
        # More dense blocks
        for i in range(4):
            x = self.densenet_block(x, growth_rate, f'conv{i+6}_block1')
        
        # Final layers
        x = layers.BatchNormalization()(x)
        x = layers.Activation('relu')(x)
        x = layers.GlobalAveragePooling2D()(x)
        x = layers.Dense(self.num_classes, activation='softmax')(x)
        
        model = models.Model(inputs, x)
        
        model.compile(
            optimizer=optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def attention_cnn(self):
        """
        CNN with attention mechanism
        """
        inputs = layers.Input(shape=self.input_shape)
        
        # Feature extraction
        x = layers.Conv2D(64, 3, padding='same', activation='relu')(inputs)
        x = layers.BatchNormalization()(x)
        x = layers.Conv2D(64, 3, padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D(2)(x)
        
        x = layers.Conv2D(128, 3, padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Conv2D(128, 3, padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D(2)(x)
        
        # Attention mechanism
        attention = layers.Conv2D(1, 1, activation='sigmoid')(x)
        x = layers.Multiply()([x, attention])
        
        x = layers.Conv2D(256, 3, padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.GlobalAveragePooling2D()(x)
        
        # Classifier
        x = layers.Dense(512, activation='relu')(x)
        x = layers.Dropout(0.5)(x)
        x = layers.Dense(self.num_classes, activation='softmax')(x)
        
        model = models.Model(inputs, x)
        
        model.compile(
            optimizer=optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model

def create_learning_rate_scheduler():
    """
    Create learning rate scheduler for training
    """
    def scheduler(epoch, lr):
        if epoch < 10:
            return lr
        elif epoch < 20:
            return lr * 0.5
        else:
            return lr * 0.1
    
    return keras.callbacks.LearningRateScheduler(scheduler)

def create_callbacks(model_name):
    """
    Create training callbacks
    """
    callbacks_list = [
        keras.callbacks.ModelCheckpoint(
            f'best_{model_name}.h5',
            monitor='val_accuracy',
            save_best_only=True,
            mode='max',
            verbose=1
        ),
        keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=10,
            restore_best_weights=True,
            verbose=1
        ),
        keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=5,
            min_lr=1e-7,
            verbose=1
        )
    ]
    
    return callbacks_list
